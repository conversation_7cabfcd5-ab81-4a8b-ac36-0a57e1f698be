/**
 * Notification Styles
 *
 * This file contains styles for the notification modal window:
 * - Modal overlay with backdrop blur
 * - Notification card with holographic effects
 * - Content styling and typography
 * - Action buttons
 * - Responsive design
 */

/* ======================================================
NOTIFICATION MODAL OVERLAY
====================================================== */

.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-medium), visibility var(--transition-medium);
}

.notification-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* ======================================================
NOTIFICATION CARD
====================================================== */

.notification-card {
  position: relative;
  width: 90%;
  max-width: 500px;
  background: var(--color-card-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  transform: scale(0.8) translateY(20px);
  opacity: 0;
  transition: transform var(--transition-elastic), opacity var(--transition-medium);
  overflow: hidden;
}

.notification-overlay.active .notification-card {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* Notification card background and effects */
.notification-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: var(--radius-lg);
  z-index: 1;
}

.notification-card::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: linear-gradient(135deg, rgba(79, 209, 197, 0.1), rgba(230, 255, 250, 0.1));
  border-radius: calc(var(--radius-lg) - 1px);
  z-index: 2;
}

/* ======================================================
NOTIFICATION CONTENT
====================================================== */

.notification-content {
  position: relative;
  z-index: 10;
}

.notification-title {
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--color-text-dark);
  text-align: center;
  margin-bottom: var(--spacing-lg);
  text-shadow: var(--text-shadow-light);
}

.notification-message {
  font-family: var(--font-secondary);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--color-text-medium);
  margin-bottom: var(--spacing-lg);
  text-align: left;
}

.notification-message p {
  margin-bottom: var(--spacing-md);
}

.notification-message p:last-child {
  margin-bottom: 0;
}

/* Service area link styling */
.service-area-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 600;
  transition: color var(--transition-fast);
  cursor: pointer;
  border-bottom: 1px solid transparent;
  transition: color var(--transition-fast), border-bottom-color var(--transition-fast);
}

.service-area-link:hover {
  color: var(--color-primary-dark);
  border-bottom-color: var(--color-primary-dark);
}

.service-area-link:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* ======================================================
NOTIFICATION BUTTONS
====================================================== */

.notification-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-xl);
}

.notification-button {
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 120px;
  position: relative;
  overflow: hidden;
}

.notification-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-medium);
}

.notification-button:hover::before {
  left: 100%;
}

/* Cancel button */
.notification-button.cancel {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: var(--color-text-medium);
  border: 1px solid #dee2e6;
}

.notification-button.cancel:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: var(--color-text-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Continue button */
.notification-button.continue {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  color: white;
  border: 1px solid var(--color-primary-dark);
}

.notification-button.continue:hover {
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.notification-button.continue:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* ======================================================
HOLOGRAPHIC EFFECTS FOR NOTIFICATION
====================================================== */

.notification-holographic {
  position: absolute;
  inset: 0;
  background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExMWl2eWhkZm9jMnl2b2R4YnR3ZGw0cmd1eTFtdThoYmRrdHV2NDR3YyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/WWSPhALYIBk1wtIwGZ/giphy.gif);
  background-size: 100% 100%;
  background-position: center;
  mix-blend-mode: overlay;
  opacity: 0.1;
  filter: hue-rotate(0deg) brightness(1) blur(0px);
  z-index: 3;
  pointer-events: none;
  border-radius: var(--radius-lg);
}

.notification-sparkles {
  position: absolute;
  inset: 0;
  background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExejZmZHBtbTl4ZjF5azcxa2l0eXg4eDR2MHM3ZWsxam15cWlmb3A3cCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/dZS6D8OxCz8DHs7HbJ/giphy.gif);
  background-size: 100%;
  background-position: center;
  mix-blend-mode: overlay;
  opacity: 0.05;
  filter: hue-rotate(0deg) brightness(1) blur(0px);
  z-index: 4;
  pointer-events: none;
  border-radius: var(--radius-lg);
}

/* ======================================================
RESPONSIVE DESIGN
====================================================== */

@media (max-width: 768px) {
  .notification-card {
    width: 95%;
    padding: var(--spacing-lg);
    margin: var(--spacing-md);
  }

  .notification-title {
    font-size: 1.3rem;
  }

  .notification-message {
    font-size: 0.95rem;
  }

  .notification-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .notification-button {
    width: 100%;
    padding: var(--spacing-md);
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .notification-card {
    padding: var(--spacing-md);
  }

  .notification-title {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
  }

  .notification-message {
    font-size: 0.9rem;
    margin-bottom: var(--spacing-md);
  }

  .notification-buttons {
    margin-top: var(--spacing-lg);
  }
}

/* ======================================================
ANIMATIONS
====================================================== */

@keyframes notification-fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes notification-fadeOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
}

.notification-card.fade-in {
  animation: notification-fadeIn var(--transition-elastic) forwards;
}

.notification-card.fade-out {
  animation: notification-fadeOut var(--transition-medium) forwards;
}

