/**
 * Main Application Entry Point
 *
 * This file initializes all modules and sets up the application.
 * It handles:
 * - Module initialization
 * - Event listener setup
 * - Cleanup on page unload
 * - Error handling for critical operations
 */

// Import modules
import { initCardEffects } from './modules/card-effects.js';
import { initPageTransitions } from './modules/page-transitions.js';
import { initServices } from './modules/services.js';
import { initDeviceDetection } from './modules/device-detection.js';
import { initServiceArea } from './modules/service-area.js';

// Module instances for cleanup
let cardEffects;
let pageTransitions;
let services;
let deviceDetection;
let serviceArea;

/**
 * Initializes all application modules
 */
function initializeApp() {
    try {
        // Initialize device detection first as other modules may depend on it
        deviceDetection = initDeviceDetection();

        // Get the main card element
        const card = document.getElementById('card');
        if (!card) {
            throw new Error('Card element not found in the DOM');
        }

        // Initialize modules in sequence with error handling
        try {
            cardEffects = initCardEffects(card);
        } catch (error) {
            console.error('Failed to initialize card effects:', error);
        }

        try {
            pageTransitions = initPageTransitions(card);
        } catch (error) {
            console.error('Failed to initialize page transitions:', error);
        }

        try {
            services = initServices();
        } catch (error) {
            console.error('Failed to initialize services:', error);
        }

        try {
            serviceArea = initServiceArea();
        } catch (error) {
            console.error('Failed to initialize service area:', error);
        }
    } catch (error) {
        console.error('Critical error during application initialization:', error);
        // Display user-friendly error message if possible
        try {
            const errorMessage = document.createElement('div');
            errorMessage.style.position = 'fixed';
            errorMessage.style.top = '50%';
            errorMessage.style.left = '50%';
            errorMessage.style.transform = 'translate(-50%, -50%)';
            errorMessage.style.padding = '20px';
            errorMessage.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
            errorMessage.style.color = 'white';
            errorMessage.style.borderRadius = '5px';
            errorMessage.style.textAlign = 'center';
            errorMessage.style.zIndex = '10000';
            errorMessage.innerHTML = '<strong>Something went wrong</strong><br>Please try refreshing the page.';
            document.body.appendChild(errorMessage);
        } catch (e) {
            // Last resort - can't even show error message
        }
    }
}

// Initialize the application when the DOM is ready
document.addEventListener('DOMContentLoaded', initializeApp);

// Handle page restoration from browser cache (back/forward navigation)
window.addEventListener('pageshow', (event) => {
    // If the page was restored from cache, re-initialize the application
    if (event.persisted) {
        console.log('Page restored from cache, re-initializing...');

        // Re-initialize page transitions specifically to fix button event listeners
        if (pageTransitions && pageTransitions.reinitialize) {
            pageTransitions.reinitialize();
        }

        // Re-initialize other modules if needed
        try {
            if (cardEffects && cardEffects.reinitialize) {
                cardEffects.reinitialize();
            }
        } catch (error) {
            console.error('Error re-initializing card effects:', error);
        }

        try {
            if (services && services.reinitialize) {
                services.reinitialize();
            }
        } catch (error) {
            console.error('Error re-initializing services:', error);
        }

        // Re-setup service area navigation
        try {
            setupServiceAreaNavigation();
        } catch (error) {
            console.error('Error re-initializing service area navigation:', error);
        }
    }
});

/**
 * Cleans up all module event listeners and resources
 */
function cleanupApp() {
    // Clean up all event listeners
    try {
        if (cardEffects && cardEffects.cleanup) {
            cardEffects.cleanup();
        }
    } catch (error) {
        console.error('Error during card effects cleanup:', error);
    }

    try {
        if (pageTransitions && pageTransitions.cleanup) {
            pageTransitions.cleanup();
        }
    } catch (error) {
        console.error('Error during page transitions cleanup:', error);
    }

    try {
        if (services && services.cleanup) {
            services.cleanup();
        }
    } catch (error) {
        console.error('Error during services cleanup:', error);
    }

    try {
        if (deviceDetection && deviceDetection.cleanup) {
            deviceDetection.cleanup();
        }
    } catch (error) {
        console.error('Error during device detection cleanup:', error);
    }

    try {
        if (serviceArea && serviceArea.cleanup) {
            serviceArea.cleanup();
        }
    } catch (error) {
        console.error('Error during service area cleanup:', error);
    }
}

// Clean up event listeners before page unload
window.addEventListener('beforeunload', cleanupApp);

// Track which page we came from to service area
let serviceAreaOrigin = null; // 'main-card' or 'services-page'

// Flag to prevent duplicate event listeners
let serviceAreaNavigationInitialized = false;

/**
 * Sets up service area navigation event listeners
 */
function setupServiceAreaNavigation() {
  // Prevent duplicate initialization
  if (serviceAreaNavigationInitialized) {
    return;
  }

  const servicesAreaButton = document.querySelector('.services-area-button');
  const serviceAreaButton = document.querySelector('.service-area-button'); // Main card service area button
  const serviceAreaBackButton = document.querySelector('.service-area-back-button');
  const serviceAreaPage = document.querySelector('.service-area-page');
  const servicesPage = document.querySelector('.services-page');
  const card = document.getElementById('card');
  const cardHeader = document.querySelector('.card-header');
  const contactInfo = document.querySelector('.contact-info');
  const bookButton = document.querySelector('.book-button');

  // Handle service area button from main card
  if (serviceAreaButton && serviceAreaPage && card && cardHeader && contactInfo && bookButton) {
    serviceAreaButton.addEventListener('click', () => {
      // Track that we came from main card
      serviceAreaOrigin = 'main-card';

      // Hide main card elements
      cardHeader.classList.add('inactive');
      contactInfo.classList.add('inactive');
      bookButton.classList.add('inactive');

      // Show service area page
      serviceAreaPage.style.display = 'block';
      serviceAreaPage.style.opacity = '1';
      card.classList.add('services-active');
      card.classList.remove('card-default');

      // Initialize map after page is shown
      setTimeout(() => {
        if (typeof window.initMap === 'function') {
          window.initMap();
        }
      }, 100);
    });
  }

  // Handle service area button from services page
  if (servicesAreaButton && serviceAreaBackButton && serviceAreaPage && servicesPage) {
    servicesAreaButton.addEventListener('click', () => {
      // Track that we came from services page
      serviceAreaOrigin = 'services-page';

      // Hide services page
      servicesPage.style.opacity = '0';
      servicesPage.style.animation = 'services-fadeOut 0.4s cubic-bezier(0.25, 0.1, 0.25, 1) forwards';

      // Show service area page
      setTimeout(() => {
        servicesPage.style.display = 'none';
        serviceAreaPage.style.display = 'block';
        serviceAreaPage.style.opacity = '1';

        // Initialize map after page is shown
        setTimeout(() => {
          if (typeof window.initMap === 'function') {
            window.initMap();
          }
        }, 100);
      }, 400);
    });

    serviceAreaBackButton.addEventListener('click', () => {
      // Clean up map before navigating away
      if (typeof window.cleanupMap === 'function') {
        window.cleanupMap();
      }

      // Navigate back based on where we came from
      if (serviceAreaOrigin === 'services-page') {
        // Return to services page
        serviceAreaPage.style.opacity = '0';
        setTimeout(() => {
          serviceAreaPage.style.display = 'none';
          servicesPage.style.display = 'block';
          servicesPage.style.opacity = '1';
          servicesPage.style.animation = 'services-fadeIn 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.4) forwards';
        }, 100);
      } else if (serviceAreaOrigin === 'main-card') {
        // Return to main card
        serviceAreaPage.style.opacity = '0';
        setTimeout(() => {
          serviceAreaPage.style.display = 'none';
          card.classList.remove('services-active');
          card.classList.add('card-default');

          // Show main card elements
          cardHeader.classList.remove('inactive');
          contactInfo.classList.remove('inactive');
          bookButton.classList.remove('inactive');
        }, 100);
      }

      // Reset origin tracking
      serviceAreaOrigin = null;
    });
  }

  // Mark as initialized to prevent duplicate event listeners
  serviceAreaNavigationInitialized = true;
}

// Service area navigation integration with existing page transitions
document.addEventListener('DOMContentLoaded', setupServiceAreaNavigation);

// Also set up service area navigation when page is restored from cache
// Note: This is handled by the main pageshow event listener above

